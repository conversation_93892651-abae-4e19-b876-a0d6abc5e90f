<template>
  <div id="app" :style="{ height: viewportHeight }">
    <router-view></router-view>
    <Footer />
  </div>
</template>

<script>
import Footer from './common/Footer'
export default {
  name: 'app',
  components: {
    Footer
  },
  data() {
    return {
      status:0
    };
  },
  methods: {
    getBaseInfo(){
      this.$http({
        method: 'get',
        url: 'base_info'
      }).then(res=>{
        if(!localStorage.getItem('token') && this.$route.path != "/Login"){
          this.$router.push({path:'/Login'})
        }
        this.$store.commit('setBaseInfoValue', res.data);
      })
    },
    resetHeight() {
      const viewportHeight = window.innerHeight
      this.viewportHeight = `${viewportHeight}px`
      console.log(viewportHeight)
    },

  },
  created(){
    this.getBaseInfo();
    this.resetHeight()


    
    const i = 'orientationchange' in window ? 'orientationchange' : 'resize'
    const setViewHeight = () => {
      const windowVH = window.innerHeight / 100
      document.documentElement.style.setProperty('--vh', windowVH + 'px')
    }
    document.addEventListener('DOMContentLoaded', setViewHeight)
    window.addEventListener(i, setViewHeight)
  }
}
</script>

<style>
html, body{
  overflow: hidden;
}
#app {
  width: 31.25rem;
  margin: 0 auto;
  /* height: var(--vh) * 100; */
  /* background: #f0f0f0; */
  position: relative;
  overflow-y:auto;
  box-shadow: 0 0 .13333rem rgba(0, 0, 0, .2);
}
body .van-toast {
  font-size: 1.38rem;
  padding: 1.00rem;
  line-height: 2.13rem;
  width: 7rem;
  height: 7rem;
  border-radius: 0.50rem;
  background: linear-gradient(30deg,rgba(245,96,205,.8),rgba(79,43,138,.78));
}
body .van-toast .van-toast__icon {
  font-size: 2.13rem;
}
::v-deep .van-toast .van-loading__spinner{
  width: 2.50rem !important;
  height: 2.50rem !important;
}
*, :after, :before {
  box-sizing: border-box;
}

#content{
  overflow-y: auto;
  padding-top: 2.875rem;
}


.van-nav-bar {
  background: url(/public/img/home/<USER>/ 100%;
  height: 2.875rem;
  position: fixed !important;
  z-index: 1000 !important;
  max-width: 31.25rem;
  width: 100%;
  top: 0;
}

.van-nav-bar__content {
  height: 2.875rem !important;
  line-height: 2.875rem !important;
}

.van-nav-bar__title{
  display: flex !important;
}


@media (max-width: 31.25rem) {
    #app {
        width: 100%;
    }
}

.van-toast{
  background: linear-gradient(30deg, rgba(245, 96, 205, .8), rgba(79, 43, 138, .78));
}

.van-pull-refresh__head  span{
  font-size: 1.00rem !important;
}
.van-pull-refresh__head  div{
  font-size: 1.00rem !important;
}
.van-loading {
  height: 3.13rem !important;
  line-height: 3.13rem !important;
}
.van-pull-refresh__head {
  height: 3.13rem !important;
  line-height: 3.13rem !important;
}
</style>
