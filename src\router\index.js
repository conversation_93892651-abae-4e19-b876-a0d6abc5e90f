import Vue from 'vue'
import VueRouter from 'vue-router'
//首页
import Home from '../pages/home/<USER>'/* 首页 */
import XuanfeiDetail from '../pages/home/<USER>'/* 首页 */
import Mine from '../pages/mine/index.vue'/* 我的 */
import Choose from '../pages/choose/index.vue'/* 选妃 */
import List from '../pages/choose/list.vue'/* 选妃列表 */
import Profile from '../pages/choose/profile.vue'/* 选妃详情 */
import Video from '../pages/video/index.vue'/* 视频 */
import Game from '../pages/game/index.vue'/* 游戏 */
import Login from '../pages/login/index.vue'/* 登录 */
import Register from '../pages/login/register.vue'/* 注册 */
import ServiceOnline from '../pages/mine/ServiceOnline.vue'/* 客服列表 */
import ServicePage from '../pages/mine/ServicePage.vue'/* 客服详情界面 */
import Setting from '../pages/mine/Setting.vue'/* 设置 */
import Infomation from '../pages/mine/Infomation.vue'/* 基本信息 */
import Setname from '../pages/mine/Setname.vue'/* 修改姓名 */
import Language from '../pages/mine/Language.vue'/* 语言选择 */
import Setsex from '../pages/mine/Setsex.vue'/* 修改姓名 */
import Recharge from '../pages/mine/Recharge.vue'/* 充值 */
import SetPayPassword from '../pages/mine/SetPayPassword.vue'/* 修改提现密码 */
import SetLoginPassword from '../pages/mine/SetLoginPassword.vue'/* 修改提现密码 */
import Lottery from '../pages/lottery/index.vue'/* 彩票详情 */
import Notice from '../pages/mine/Notice.vue'/* 公告 */
import PlayVideo from '../pages/video/PlayVideo'/* 视频播放页面 */
import Setbank from '../pages/mine/Setbank'/* 视频播放页面 */
import BindCard from '../pages/mine/BindCard'/* 绑定银行卡界面 */
import Withdraw from '../pages/mine/Withdraw'/* 绑定银行卡界面 */
import Personalreport from '../pages/mine/Personalreport'/* 个人表报 */
import GameRecord from '../pages/mine/GameRecord'/* 游戏记录 */
import GameRecordDetail from '../pages/mine/GameRecordDetail'/* 游戏记录 */
import Liushui from '../pages/mine/Liushui'/* 流水 */
import WithdrawRecord from '../pages/mine/WithdrawRecord'/* 提现记录 */
import Tx from '../pages/mine/Tx'/* 充值 */
import ChongRecord from '../pages/mine/ChongRecord'/* 充值记录 */
import Goods from '../pages/goods/Goods'/* 充值记录 */
import Info from '../pages/mine/Info'/* 充值记录 */
import WindowLottery from '../pages/lottery/windowLottery'/* 充值记录 */
import Message from '../pages/message/index.vue'/* 充值记录 */
import MessageDetail from '../pages/message/detail.vue'/* 充值记录 */
import Jifen from '../pages/mine/jifen'/* 充值记录 */
import MyMove from '../pages/mine/MyMove'/* 充值记录 */


Vue.use(VueRouter)
const routes = [
  { path: '/', redirect: '/Home', component: Home, meta: { title: 'LoveBridge' } },
  { path: '/Home', name: 'home', component: Home, meta: { title: 'LoveBridge' } },
  { path: '/XuanfeiDetail', name: 'Detail', component: XuanfeiDetail, meta: { title: 'LoveBridge' } },
  { path: '/Choose', name: 'choose', component: Goods, meta: { title: 'LoveBridge' } },
  { path: '/List', name: 'list', component: List, meta: { title: 'LoveBridge' } },
  { path: '/Profile', name: 'profile', component: Profile, meta: { title: 'LoveBridge' } },
  { path: '/Mine', name: 'mine', component: Mine, meta: { title: 'LoveBridge' } },
  { path: '/Video', name: 'video', component: Video, meta: { title: 'LoveBridge' } },
  { path: '/Game', name: 'game', component: Game, meta: { title: 'LoveBridge' } },
  { path: '/Login', name: 'login', component: Login, meta: { title: 'LoveBridge' } },
  { path: '/Register', name: 'register', component: Register, meta: { title: 'LoveBridge' } },
  { path: '/ServiceOnline', name: 'ServiceOnline', component: ServiceOnline, meta: { title: 'LoveBridge' } },
  { path: '/ServicePage', name: 'ServicePage', component: ServicePage, meta: { title: 'LoveBridge' } },
  { path: '/Setting', name: 'Setting', component: Setting, meta: { title: 'LoveBridge' } },
  { path: '/Infomation', name: 'Infomation', component: Infomation, meta: { title: 'LoveBridge' } },
  { path: '/Setname', name: 'Setname', component: Setname, meta: { title: 'LoveBridge' } },
  { path: '/Setsex', name: 'Setsex', component: Setsex, meta: { title: 'LoveBridge' } },
  { path: '/Language', name: 'Language', component: Language, meta: { title: 'LoveBridge' } },
  { path: '/Recharge', name: 'Recharge', component: Recharge, meta: { title: 'LoveBridge' } },
  { path: '/SetPayPassword', name: 'SetPayPassword', component: SetPayPassword, meta: { title: 'LoveBridge' } },
  { path: '/SetLoginPassword', name: 'SetLoginPassword', component: SetLoginPassword, meta: { title: 'LoveBridge' } },
  { path: '/Lottery', name: 'Lottery', component: Lottery, meta: { title: 'LoveBridge' } },
  { path: '/WindowLottery', name: 'WindowLottery', component: WindowLottery, meta: { title: 'LoveBridge' } },
  { path: '/Notice', name: 'Notice', component: Notice, meta: { title: 'LoveBridge' } },
  { path: '/PlayVideo', name: 'PlayVideo', component: PlayVideo, meta: { title: 'LoveBridge' } },
  { path: '/Setbank', name: 'Setbank', component: Setbank, meta: { title: 'LoveBridge' } },
  { path: '/BindCard', name: 'BindCard', component: BindCard, meta: { title: 'LoveBridge' } },
  { path: '/Withdraw', name: 'Withdraw', component: Withdraw, meta: { title: 'LoveBridge' } },
  { path: '/Personalreport', name: 'Personalreport', component: Personalreport, meta: { title: 'LoveBridge' } },
  { path: '/WithdrawRecord', name: 'WithdrawRecord', component: WithdrawRecord, meta: { title: 'LoveBridge' } },
  { path: '/GameRecord', name: 'GameRecord', component: GameRecord, meta: { title: 'LoveBridge' } },
  { path: '/GameRecordDetail', name: 'GameRecordDetail', component: GameRecordDetail, meta: { title: 'LoveBridge' } },
  { path: '/Liushui', name: 'Liushui', component: Liushui, meta: { title: 'LoveBridge' } },
  { path: '/Tx', name: 'Tx', component: Tx, meta: { title: 'LoveBridge' } },
  { path: '/ChongRecord', name: 'ChongRecord', component: ChongRecord, meta: { title: 'LoveBridge' } },
  { path: '/Goods', name: 'Goods', component: Goods, meta: { title: 'LoveBridge' } },
  { path: '/Info', name: 'Info', component: Info, meta: { title: 'LoveBridge' } },
  { path: '/Message', name: 'Message', component: Message, meta: { title: 'LoveBridge' } },
  { path: '/MessageDetail', name: 'MessageDetail', component: MessageDetail, meta: { title: 'LoveBridge' } },
  { path: '/Jifen', name: 'Jifen', component: Jifen, meta: { title: 'LoveBridge' } },
  { path: '/MyMove', name: 'MyMove', component: MyMove, meta: { title: 'LoveBridge' } },


];

//生成路由实例
const router = new VueRouter({
  routes
})
router.beforeEach((to, from, next) => {         //修改标题路由配置加上这个
  document.title = to.matched[0].meta.title
  next()
})

export default router