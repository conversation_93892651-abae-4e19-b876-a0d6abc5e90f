<template>
  <div class="container page">
    <van-nav-bar class="nav-bar">
      <template #left>
        <van-icon name="arrow-left" color="#fff" @click="back()"/>
      </template>
      <template #title>
        <span style="font-size: 1rem; color: #fff;">{{ videoInfo.vod_name || title }}</span>
      </template>
    </van-nav-bar>
    <div id="content">
      <div class="movie-video">
        <video :id="videoId"  class="video-js">
        </video>
      </div>
      <div class="movie-content">
        <div class="movie-descript">
          <p>{{ this.videoInfo.vod_name || title}}</p>
          <span>{{this.videoInfo.count}}{{ $t("videoDetail.count") }}</span>
        </div>

        <div class="movie-body">
          <div class="movie-title">
            <van-image src="img/video/hot.png" width="20"></van-image>
            <span>{{ $t("videoDetail.title") }}</span>
          </div>
          
          <div class="wrap">
              <div class="video_wrap" v-for="(v,key) in moreVideoInfo" :key="key" @click="toPlayVideo(v.id)">
                <div class="video">
                  <van-image  :src="v.vod_pic"></van-image>
                  <div class="info">
                    <span class="label">{{v.vod_name}}</span>
                    <span class="value">{{$t("videoDetail.count")}}:{{v.count}}</span>
                  </div>
                </div>
              </div>
            </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import videojs from "video.js";
import "videojs-contrib-hls";
export default {
  data() {
    return {
      nowPlayVideoUrl: "",
      cover:"",
      userInfo:[],
      videoInfo:{},
      moreVideoInfo:{},
      player:null,
      is_play:false,
      times: null,
      is_see:0,
      title: '',
    };
  },
  computed: {
    videoId() {
      return `video${Date.parse(new Date())}`
    }
  },
  methods: {
    back() {
      return window.history.back();
    },
    getVideoInfo(){
      
      this.$http({
        method: 'get',
        data:{id:this.$route.query.id},
        url: 'video_get_info'
      }).then(res=>{
        this.videoInfo = res.data;
        this.nowPlayVideoUrl = this.videoInfo.vod_play_url;
        this.cover = this.videoInfo.vod_pic;
        this.player.poster = this.cover
        this.getVideo();
      })

    },
    toPlayVideo(id){
      if(!localStorage.getItem('token')){
        this.$router.push({path:'/Login'})
      }else {
        this.$router.push({path:'?id='+id})
        location.reload();
      }

    },
    getMoreVideoItem(){
      this.$http({
        method: 'get',
        url: 'video_get_more_item'
      }).then(res=>{
          this.moreVideoInfo = res.data;
      })
    },
    getVideo() {
      // this.player = videojs("my-video",  {
      //   height:"200px",
      //   preload: "auto", // 预加载
      //   controls: true,  // 显示播放的控件
      //   multipleArray: [0.75, 1, 1.5, 2], // 倍速设置
      // });
      this.player.src([{
        src: this.nowPlayVideoUrl,
        type: "application/x-mpegURL" // 告诉videojs,这是一个hls流
      }]);
    },
    getUserInfo(){
      this.$http({
        method: 'get',
        url: 'user_info'
      }).then(res=>{
        if(res.code === 200){
          this.userInfo = res.data;
          this.is_see = this.userInfo.is_see;
          if(this.userInfo.status !== 1){
            this.$toast(this.$t("videoDetail.account_out"));
            localStorage.clear()
            this.$router.push({path:'/Login'})
          }else {
            if(this.$store.getters.getBaseInfo.isplay == 1){
              this.getVideoInfo();
              this.getMoreVideoItem()
              // if(this.userInfo.money <= "0.00"){
              //   this.$toast(this.$t("videoDetail.buy"));
              //   this.$router.push({path:'/Home'})
              // }
            }else {
              this.getVideoInfo();
              this.getMoreVideoItem();
            }
          }
        }else if(res.code ===401){
          this.$toast(res.msg);
        }
      })
    },
  },
  mounted(){
    const _this = this;
    this.title = this.$route.query.title
    if(!localStorage.getItem('token')){
      this.$router.push({path:'/Login'})
    }else {
      this.player = videojs(this.videoId,  {
        height:"240px",
        preload: "auto", // 预加载
        controls: true,  // 显示播放的控件
        multipleArray: [0.75, 1, 1.5, 2], // 倍速设置
      },function(){
        this.on("play",() => {
          _this.is_play=true;
        });
      });
      this.getUserInfo();
      this.times = setInterval(() => {
        if(this.is_play && this.is_see == 0){
          const ct = Math.round(this.player.currentTime())
          if(ct >= 180){
            this.player.pause()
            this.$toast(this.$t("videoDetail.buy"));
            return
          }
        } 
      }, 1000 * 2);
    }

  },

  destroyed () {
    if(this.is_play){
      this.is_play = false;
    }
    clearInterval(this.times);
  }
};
</script>

<style scoped lang="less">
.video-js {
  width: 100%;
  /*height: 420px;*/
  font-size: 24px;
}
.movie-content{
  flex: 1;
  overflow-y: auto;
}
.movie-content .movie-descript{
  width: 100%;
  // height: 140px;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: space-between;
  padding: 15px;
  span {
    padding: 0.38rem 0.69rem;
    background: #f2f2f5;
    color: #aa2ddc;
    display: block;
    font-size: .88rem;
    border-radius: .3rem;
    // margin-bottom: 0.6rem;
  }
}
.movie-content .movie-descript p{
  font-size: 30px;
  font-weight: 700;
  color: #000;
}
.movie-content .movie-descript span{
  // color: #979799;
}
.movie-content .movie-body{
  width: calc(100% - 20px);
  margin: 0 auto;
}
.movie-content .movie-body .movie-title{
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding-top: 12px;
  span{
    color: #4b358b;
    margin-left: 8px;
    display: inline-block
  }
}
.movie-content .movie-body .movie-title>div:first-child {
  width: 410px;
}
.movie-content .movie-body .movie-title>div:first-child span{
  display: flex;
  align-items: center;
  justify-content: flex-start;
  font-size: 30px;
  font-weight: 700;
  color: #000;
}
.movie-content .movie-body .movie-title>div:first-child span:before {
  content: "";
  display: block;
  width: 8px;
  height: 30px;
  background-color: #7e5678;
  border-radius: 25px;
  margin-right: 10px;
}
.movie-play-item{
  width: 100%;
  height: 200px;
  border-radius: 10px;
  position: relative;
  display: flex;
  background-color: #fff;
  margin-bottom: 20px;
}
.movie-play-item>div{
  height: 100%;
}
.movie-play-item>div:first-child {
  width: 200px;
  position: relative;
}
.movie-play-item>div:first-child>img{
  width: 100%;
  height: 100%;
  border-radius: 10px 0 0 10px;
}
.movie-play-item>div:first-child>div{
  position: absolute;
  width: 100%;
  height: 30px;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 10px;
  background-color: rgba(0,0,0,.4);
  border-radius: 0 0 0 10px;
}
.movie-play-item>div:first-child>div .van-count-down {
  color: #fff;
  font-size: 25px;
}
.movie-play-item>div:nth-child(2) p{
  width: 500px;
  height: 60px;
  font-size: 30px;
  line-height: 32px;
  word-break: break-all;
  overflow: hidden;
  color: #000;
}
.movie-play-item>div:nth-child(2) span{
  color: #000;
}
.movie-play-item>div:nth-child(2) {
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: space-between;
}
abbr, address, article, aside, audio, b, blockquote, body, canvas, caption, cite, code, dd, del, details, dfn, div, dl, dt, em, fieldset, figcaption, figure, footer, form, h1, h2, h3, h4, h5, h6, header, hgroup, html, i, iframe, img, ins, kbd, label, legend, li, mark, menu, nav, object, ol, p, pre, q, samp, section, small, span, strong, sub, summary, sup, table, tbody, td, tfoot, th, thead, time, tr, ul, var, video {
  margin: 0;
  padding: 0;
  border: 0;
  outline: 0;
  font-size: 100%;
  vertical-align: baseline;
  box-sizing: border-box;
}

.vjs-big-play-button .vjs-icon-placeholder {
  font-size: 1.63em !important;
}
.wrap{
  display: flex;
  flex-wrap: wrap;
  padding: 0 0.3rem 1rem;
  .video_wrap {
    height: 7.50rem;
    width: 50%;
    margin-top: 0.75rem;
  }
  .video {
    margin: 0 0.3rem;
    height: 7.50rem;
    position: relative;
    border-radius: 0.33rem;
    overflow: hidden;
    .van-image{
      width: 100%;
      height: 100%;
    }
    .info {
      height: 1.88rem;
      width: 100%;
      position: absolute;
      background: #00000066;
      padding: 0.38rem 0;
      z-index: 10000;
      bottom: 0;
      display: flex;
      .label, .value {
        width: 50%;
        padding: 0 0.38rem;
        font-size: 0.88rem;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        color: #fff;
        text-align: center;
      }
      
    }
  }
}
</style>