<template>
  <div class="movie-hall page">
    <van-nav-bar >
      <template #title>
        <span style="font-size: 1rem; color: #fff;">{{ $t('video.video') }}</span>
      </template>
    </van-nav-bar>
    <div id="content">
      <van-tabs v-model="active" animated swipeable  @change="OnChange">
        <van-tab v-for="(v,key) in videolitem" :key="key" :title="v.name" :name="v.key" ></van-tab>
      </van-tabs>
      <div class="movie-list-tab">
        <van-pull-refresh v-model="isLoading" @refresh="refresher" class="container" :pulling-text="$t('common.pullLabel1')" :loosing-text="$t('common.pullLabel2')" :loading-text="$t('common.pullLabel3')">
          <Nodata v-if="videolist.length === 0" />
          <van-list v-model="loading" :finished="finished" :finished-text="$t('common.nomore')" :loading-text="$t('common.loading')" :error-text="$t('common.error')" @load="onLoad">
            <div class="wrap">
              <div class="video_wrap" v-for="(v,key) in videolist" :key="key" @click="toPlayVideo(v)">
                <div class="video">
                  <van-image  :src="v.vod_pic"></van-image>
                  <div class="info">
                    <span class="label">{{v.vod_name}}</span>
                    <span class="value">{{$t("video.play")}}:{{v.count}}</span>
                  </div>
                </div>
              </div>
            </div>
          </van-list>
        </van-pull-refresh>
      </div>
    </div>
  </div>
</template>

<script>
import { Toast } from 'vant';
import Nodata from "@/common/Nodata.vue";
export default {
  components: { Nodata },
  data() {
    return {
      active: 0,
      isLoading: false,
      count:0,
      loading: false,
      finished: false,
      refreshing: false,
      videolitem: [],
      videolist: [],
      number:0,
      page:0,
      videoSwiperOption: {
        slidesPerView: 'auto',
        spaceBetween: 0,
        slidesPerGroup : 1,
      }
    };
  },
  computed: {
    type() {
      return this.videolitem.filter((item, index) => index === this.active)?.[0]?.id || 1
    }
  },
  methods: {
    getVideoClass(){
      return this.$http({
        method: 'get',
        url: 'video_class'
      }).then(res=>{
        this.videolitem = res.data;
      })
    },
    toPlayVideo(v){
      if(!localStorage.getItem('token')){
        this.$router.push({path:'/Login'})
      }else {
        this.$router.push({path:`/PlayVideo?id=${v.id}&title=${v.vod_name}`})
      }

    },
    async getVideoList(refresh=true){
      if(this.videolitem.length === 0) {
        await this.getVideoClass();//获取视频类目
      }
      return this.$http({
        method: 'get',
        data:{id:this.type,page:this.page},
        url: 'video_list'
      }).then(res=>{
        this.count = res.data.count;
        if(refresh) {
          this.videolist = res.data.data
        }else {
          this.videolist = [...this.videolist, ...res.data.data]
        }
        this.loading = false
        if(this.videolist.length >= this.count) {
          this.finished = true
        }
      })
    },
    onLoad() {
      console.log('onload')
      this.page ++
      this.getVideoList(false);
    },
    OnChange(){
      this.videolist = [];
      this.number = 0;
      this.page = 0;
      this.count = 0;
      this.finished = false
      this.loading = true
      this.getVideoList();//获取视频列表
    },

    async refresher() {
      this.isLoading = true
      this.page = 1
      await this.getVideoList()
      this.finished = false
      this.isLoading = false
    },
  },
  created() {
    // this.getVideoClass();//获取视频类目
    // this.OnChange()
  }
};
</script>

<style lang='less' scoped>
.page{
  position: absolute!important;
  top: 0;
  left: 0;
  right: 0;
  background-color: #f2f2f5;
}

::v-deep .van-tabs__nav {
  background: #fff;
}
::v-deep .van-tab {
  color: #442889;
  font-size: 0.88rem;
}
::v-deep .van-tabs__line {
  bottom: 0.94rem;
  width: 2.50rem;
  height: 0.25rem;
  border-radius: 0.31rem;
  background: linear-gradient(90deg,#a46efb,#f4f2fc,#ebd8f2);
}
::v-deep .van-tabs--line .van-tabs__wrap {
  height: 2.75rem;
}
::v-deep .van-tabs__wrap--scrollable .van-tab {
  padding: 0 1rem;
}
::v-deep  .van-hairline--bottom::after {
  border-bottom-width: 0.00rem;
}
::v-deep  .van-tab--active {
  font-weight: bold;
  font-size: 1.06rem;
}

.video_swiper {
  width: 100%;
  flex: 1;
  .swiper-slide {
    flex-shrink: 0;
    flex-grow: 0;
    flex-basis: 100%;
    justify-content: center;
    height: 100%;
    position: relative;
    transition-property: transform;
  }
}
.movie-list-tab {
  overflow: auto;
  height: 100%;
}
::v-deep .van-pull-refresh__track .van-pull-refresh__head *{
  color: #000;
  font-size: 2.19rem;
}
.movie-list-tab .hot-recommend-div{
  height: 100%;
  margin: 0.63rem auto;
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  flex-wrap: wrap;
  //overflow: auto;
}
.list-item{
  display: flex;
  width: calc(100% - 3.13rem);
  margin: 0.63rem auto;
  align-items: flex-start;
  justify-content: flex-start;
  flex-wrap: wrap;
}
.list-item .movie-list-item:nth-child(odd) {
  // margin-right: 1.25rem;
}
.movie-list-item .cover_img{
  border-radius: 0.25rem;
  width:13.13rem;
  height:7.50rem;
}
.movie-list-item{
  margin-bottom: -0.63rem;
  width: 48%;
  margin: 1%;
}
.list-item .movie-list-item-bottom{
  position: relative;
  width: 20.94rem;
  bottom: 2.63rem;
}
.list-item .movie-list-item-bottom .movie-time-div{
  background-color: rgba(0,0,0,.4);
}
.list-item .movie-list-item-bottom>div {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.list-item .movie-list-item-bottom .movie-time-div .van-count-down {
  color: #fff;
}
.list-item .movie-list-item .movie-time-div span:first-child {
  overflow: hidden;
  white-space: nowrap;
  width: 11.25rem;
  padding-left: 0.50rem;
  font-size: 1.56rem;
}
.list-item .movie-time-div {
  color: #fff;
  border-radius: 0 0 1.25rem 1.25rem;
  height: 2.19rem;
}



.container {
  height: calc(var(--vh) * 100 - 2.88rem - 2.75rem - 3.13rem);
  background: #fff;
  overflow-y: auto;
}
.wrap{
  display: flex;
  flex-wrap: wrap;
  padding: 0 0.3rem;
  .video_wrap {
    height: 7.50rem;
    width: 50%;
    margin-top: 0.75rem;
  }
  .video {
    margin: 0 0.3rem;
    height: 7.50rem;
    position: relative;
    border-radius: 0.33rem;
    overflow: hidden;
    .van-image{
      width: 100%;
      height: 100%;
    }
    .info {
      height: 1.88rem;
      width: 100%;
      position: absolute;
      background: #00000066;
      padding: 0.38rem 0;
      z-index: 10000;
      bottom: 0;
      display: flex;
      .label, .value {
        width: 50%;
        padding: 0 0.38rem;
        font-size: 0.88rem;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        color: #fff;
        text-align: center;
      }
      
    }
  }
}

::v-deep .van-list__finished-text{
  font-size: 1.00rem !important;
  height: 3.13rem;
  line-height: 3.13rem;
}
::v-deep .van-loading__text {
  font-size: 1.00rem !important;
}
</style>
