<template>
  <div class="content">
    <div class="top">
      <div class="setting">
        <!-- <div class="icon" @click="toKefu()">
          <van-image src="img/home/<USER>" width="24"></van-image>
        </div> -->
      </div>
      <div class="info">
        <van-image round class="user_img" :src="userInfo.header_img">
          <template v-slot:error>
            <van-image src="img/nan/11.png"></van-image>
          </template>
        </van-image>
        <div class="user_info">
          <div class="user">
            <div>{{ userInfo.username }}</div>
            <van-image src="img/user/vip.png" width="90"></van-image>
          </div>
          <div class="score">{{ $t('user.label') }}: {{ changeMoney(userInfo.xinyufen) }}</div>
          <div class="line">
            <div class="process"><div class="inside"></div></div>
            <div class="label">{{ $t('user.process') }}{{ changeMoney(userInfo.total_recharge_money) }}</div>
            <div class="value"></div>
          </div>
        </div>
      </div>
    </div>


    <div class="center">
      <div class="card1">
        <div class="left" @click="goPage('/Tx')">
          <van-image src="img/user/top_icon1.png" width="26"></van-image>
          <div>{{ $t("user.recharge") }}</div>
        </div>
        <div class="right" @click="doWithdrawal()">
          <van-image src="img/user/top_icon2.png" width="26"></van-image>
          <div>{{ $t("user.withdraw") }}</div>
        </div>
      </div>

      <div class="card2 animate__animated animate__flipInX" >
        <div>
          <div style="margin-bottom: 10%;"> {{$t("user.balance")}} </div>
          <div class="jiner"> {{changeMoney(this.userInfo.money)}} </div>
        </div>
        <div style="display: flex;">
          <span class="font-28 font-gray" style="display: flex; align-items: center;">{{$t("user.unit")}}</span>
          <div class="refresh-btn">
            <i class="van-icon van-icon-replay"><!----></i>
          </div>
        </div>
      </div>

      <div class="card3">
        <div class="block" v-for="(item, index) in menuList" :key="index" @click="goPage(item.url)">
          <van-image :src="item.icon" width="26"></van-image>
          <div class="label">{{ item.label }}</div>
        </div>
      </div>

      <div class="btn" @click="loginout()">{{ $t('user.logout') }}</div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      userInfo:{},
      menuList: [
        { label: this.$t("user.icon1"), icon: '/img/user/icon1.png', url: '/Info' },
        { label: this.$t("user.icon8"), icon: '/img/user/icon2.png', url: '/Jifen' },
        { label: this.$t("user.icon3"), icon: '/img/user/icon3.png', url: '/Liushui' },
        { label: this.$t("user.icon4"), icon: '/img/user/icon4.png', url: '/GameRecord' },
        { label: this.$t("user.icon5"), icon: '/img/user/icon5.png', url: '/Notice' },
        { label: this.$t("user.icon6"), icon: '/img/user/icon6.png', url: '/MyMove' },
        { label: this.$t("user.icon2"), icon: '/img/user/icon7.png', url: '/SetLoginPassword' },
        { label: this.$t("user.icon7"), icon: '/img/user/icon8.png', url: '/SetPayPassword' },
      ],
      isLoading: false,
    };
  },
  methods: {
    goPage(url) {
      this.$router.push({ path: url })
    },
    refresh(){
      this.isLoading = true;
      setTimeout(() => {
        this.isLoading = false;
        if(localStorage.getItem('token')){
          this.$toast(this.$t("reservation.refresh"));
        }else{
          this.$router.push({path:'/Login'})
        }
      }, 500);
    },
    exit(){
      this.$router.push("Setbank");
    },
    showSetting() {
      if(localStorage.getItem('token')){
        this.$router.push({path:'/Setting'})
      }else{
        this.$router.push({path:'/Login'})
      }
    },
    toNotice(){
      if(localStorage.getItem('token')){
        this.$router.push({path:'/Notice'})
      }else{
        this.$router.push({path:'/Login'})
      }
    },
    onRefresh() {
      setTimeout(() => {
        this.isLoading = false;
        if(localStorage.getItem('token')){
            this.getUserInfo();
            this.$toast(this.$t("reservation.refresh"));
        }else{
            this.$router.push({path:'/Login'})
        }
      }, 500);
    },
    doLogin(){
        if(localStorage.getItem('token')){
            this.$router.push({path:'/Infomation'});
        }else {
            this.$router.push({path:'/Login'})
        }
    },
    doPay(){
      this.$router.push({
        name:'Recharge',
        params:{
          'balance':this.userInfo.money
        }
      })
    },
    doWithdrawal(){
      this.$http({
        method: 'get',
        url: 'user_get_bank'
      }).then(res=>{
        console.log(res)
          if(res.data.is_bank){
            this.$router.push("withdraw");
          }else {
            this.$router.push("bindCard");
            this.$toast.fail(this.$t("setting.set_bank"));
          }
      })
    },
    toService(){
      if(this.$store.getters.getBaseInfo.iskefu == 1){
        this.$router.push("Tx");
      }else {
        this.$router.push("Tx");
        this.$toast.fail(this.$t("setting.forbid"));
      }
    },
    toKefu() {
      console.log(this.$store.getters.getBaseInfo)
      window.open(this.$store.getters.getBaseInfo.kefu)
      // this.$router.push("ServiceOnline");
    },
    getUserInfo(){
      this.$http({
        method: 'get',
        url: 'user_info'
      }).then(res=>{
        if(res.code === 200){
          console.log(res)
            this.userInfo = res.data;
            this.menu_top = 15;
            if(this.userInfo.status !== 1){
              this.$toast(this.$t("video.account_out"));
              localStorage.clear()
              this.$router.push({path:'/Login'})
            }
        }else if(res.code ===401){
          this.$toast(res.msg);
        }
      })
    },
    loginout(){
        localStorage.clear()
        this.$router.push({path:'/Login'});
    },
    
    // 价格转换（加逗号）
    changeMoney: (money, type = 'us') => {
      if (isNaN(Number(money))) {
        return '-'
      }
      money = Number(money).toFixed(0)
      const unit1 = type === 'tur' ? '.' : ','
      const x = money.split('.')
      let x1 = x[0]
      const reg = /(\d+)(\d{3})/
      while (reg.test(x1)) {
        x1 = x1.replace(reg, '$1' + unit1 + '$2')
      }
      return `${x1}`
    }
  },
  created() {
    if(localStorage.getItem('token')){
        this.getUserInfo();
    }else {
      this.userInfo.username = this.$t("setting.log_reg");
      this.userInfo.ip = this.$t("setting.more_service");
      this.userInfo.header_img = "img/mine/avatar.png";
    }
  }
};
</script>

<style lang="less" scoped>
.content{
  background: #f2f2f5;
  overflow: auto;
  height: 100%;
  padding-bottom: 3.2rem;
}

.top{
  height: 15rem;
  background: url(/public/img/user/usertop.png) no-repeat bottom / 160%;
  .setting {
    height: 3.13rem;
    display: flex;
    justify-content: end;
    .icon{
      width: 3.13rem;
      height: 3.13rem;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }

  .info {
    margin-top: 1.5rem;
    padding: 0 1.88rem;
    display: flex;
    .user_img {
      height: 4.75rem;
      width: 4.75rem;
      border: 0.25rem solid #fff;
    }
    .user_info{
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      padding: 0.63rem 0 0.63rem 1.5rem;
      .user{
        display: flex;
        align-items: center;
        div{
          color: #fff;
          font-size: 1.13rem;
          margin-right: 0.63rem;
        }
      }
      .score{
        margin: 0.25rem 0 0.13rem;
        font-size: 0.88rem;
        color: #ffe0f6;
      }
      .line{
        display: flex;
        align-items: center;
        .process{
          margin-right: 0.63rem;
          width: 6.88rem;
          height: 0.31rem;
          border-radius: 0.13rem;
          background: #e5d3cb;
          .inside{
            width: 80%;
            background: #fff;
            height: 0.31rem;
            border-radius: 0.13rem;
          }
        }
        .label,.value{
          font-size: 0.81rem;
          color: #fff;
        }
      }
    }
  }
}

.center{
  width: 94%;
  margin: -14% auto 0;
  .card1{
    background: #fff;
    border-radius: .4rem;
    padding: 1.13rem 0;
    display: flex;
    position: relative;
    >div{
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .van-image{
      margin-right: 0.75rem;
    }
    &::before{
      content: "";
      position: absolute;
      width: 0.2rem;
      left: calc(50% - .1rem);
      height: 1.5rem;
      border-radius: .46667rem;
      background: #f2f2f5;
    }
  }

  .card2{
    background: #a06098;
    box-shadow: 0 .13333rem .2rem rgba(0, 0, 0, .04);
    margin-top: 1rem;
    border-radius: .4rem;
    
    height: 6rem;
    margin-bottom: 2%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: #fff;
    padding-left: 3%;
    padding-right: 3%;
    padding-top: 4%;
    padding-bottom: 4%;

    .refresh-btn {
      margin-left: .9375rem;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 1.5625rem;
      height: 1.5625rem;
      font-size: .9375rem;
      border-radius: 50%;
      color: #9a6cd9;
      background-color: #fff;
    }
    span{
      color: #fff;
      font-size: .875rem;
    }
    .jiner {
      font-size: 1.3rem;
    }
  }

  .card3{
    background-color: #fff;
    width: 100%;
    display: flex;
    // justify-content: center;
    padding: .86667rem 0;
    align-items: center;
    flex-wrap: wrap;
    border-radius: .33333rem;
    box-shadow: 0 .13333rem .2rem rgba(0, 0, 0, .04);
    margin-top: .8rem;
    >div{
      width: 33.3333%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      padding: 1rem 0 1rem;
    }
    .label {
      color: #432788;
      padding: .53333rem 0;
    }
  }

  .btn{
    width: 90%;
    background: linear-gradient(90deg, #f560cd, #4f2b8a);
    color: #fff;
    padding: 1rem 0;
    border-radius: .33333rem;
    font-size: .93333rem;
    cursor: pointer;
    margin: 1.66667rem auto 3.33333rem;
    text-align: center;
  }
}

.user_top {
    width: 92%;
    margin: 0 auto;
    margin-top: 6.25rem;
    flex-direction: column;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 3%;
    .user_img {
    height: 3.75rem;
    width: 3.75rem;
    border: 0.06rem solid #ff1a6d;
  }
  .ro {
    color: #fff;
    background: linear-gradient(90deg, #5820ff, #ff1a6d);
    border-radius: 1.25rem;
    display: flex;
    justify-content: space-between;
    padding-left: 3%;
    padding-right: 3%;
    align-items: center;
    font-size: .6875rem;
    padding-top: 1%;
    padding-bottom: 1%;
    .huang {
      width: 1.09375rem;
      height: .9375rem
    }
  }
}

.but {
  display: flex;
  width: 92%;
  margin: 0 auto;
  justify-content: space-between;
  margin-top: 5%;
  margin-bottom: 5%;
  .chong, .tx {
    width: 48%;
    background-size: 100% 100%;
    height: 3.5rem;
    display: flex;
    justify-content: center;
    align-items: center;
    background-repeat: no-repeat;
    color: #fff;
  }
  .tx_img {
    width: 2.5rem;
    height: 2.5rem;
    margin-right: 10%;
  }
  .chong {
    background-image: url('@/assets/imgs/mine/2.png');
  }
  .tx {
    background-image: url('@/assets/imgs/mine/1.png');
  }

}

.yue {
  width: 92%;
  margin: 0 auto;
  background-image: url('@/assets/imgs/mine/yue.png');
  background-size: 100% 100%;
  height: 6rem;
  margin-bottom: 2%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #fff;
  padding-left: 3%;
  padding-right: 3%;
  padding-top: 4%;
  padding-bottom: 4%;

  .refresh-btn {
    margin-left: .9375rem;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 1.5625rem;
    height: 1.5625rem;
    font-size: .9375rem;
    border-radius: 50%;
    color: #fff;
    background-color: #c24491;
  }
  .font-gray {
    color: #fff;
  }
  .font-28 {
    font-size: .875rem;
  }
  .jiner {
    font-size: 1.3rem;
  }
}
.menu {
  width: 92%;
  background: #22063a;
  margin: 0 auto;
  margin-top: 5%;
  border-radius: 0.63rem;
  display: flex;
  flex-wrap: wrap;
  padding-left: 3%;
  padding-right: 3%;
  border: 0.06rem solid #c36dfe;
  .row{
    display: flex;
    justify-content: space-between;
    width: 100%;
    align-items: center;
    padding-top: 2%;
    padding-bottom: 2%;
    .left{
      display: flex;
      align-items: center;
      img{
        width: 2.65625rem;
        height: 2.8125rem;
      }
      .label{
        color: #fff;
        font-size: .9rem;
      }
    }
  }
  .line{
    width: 100%;
    height: .0625rem;
    background: #fff;
    opacity: .12;
  }
}
.kefu{
  position: absolute;
  right: 1rem;
  top: 1rem;
  width: 2rem;
}
</style>
