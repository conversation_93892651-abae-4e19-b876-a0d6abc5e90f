<template>
  <div>
    
    <van-nav-bar>
      <template #title>
        <van-image src="img/home/<USER>" width="60"></van-image>
      </template>
    </van-nav-bar>

    <div id="content">
      <div class="banner">
        <swiper class="banner_swiper" :options="bannerSwiperOption">
          <swiper-slide v-for="(v, key) in banners" :key="key">
            <van-image class="banner_img" round :src="v.url">
              <template v-slot:loading>
                <van-loading type="circular" />
              </template>
            </van-image>
          </swiper-slide>
        </swiper>
      </div>

      <div class="content">
        <div class="txt">
          <span> HOT </span>
          <span class="icon">{{ $t('index.title1') }}</span>
        </div>
        <div class="swiper_wrap">
          <div class="swiper" :style="{ width: `calc(${9.35 * xuanfeiList.length}rem)` }">
            <div v-for="item in xuanfeiList" :key="item.id" class="swipe" @click="goDetail(item)">
              <van-image :src="item.img_url[0]"></van-image>
              <div class="tip">
                <span>{{ $t('index.tip1') }}</span>
              </div>
            </div>
          </div>
        </div>
        <div class="txt">{{ $t('index.title2') }}</div>
        <div class="ganeGroup">
          <div class="gameItem" v-for="item in gameList" :key="item.id" @click="goGame(item)">
            <div class="image">
              <van-image   :src="item.ico"></van-image>
            </div>
            <div class="label">{{ item.name }}</div>
          </div>
        </div>
        <div class="txt">{{ $t('index.title3') }}</div>
        <div class="xuanfeiList">
          <div class="row" v-for="item in xuanfeiList" :key="item.id" @click="goDetail(item)">
            <div class="left">
              <van-image :src="item.img_url[0]"></van-image>
              <div>{{ item.xuanfei_name }}</div>
              <div class="tip">
                <span>{{ $t('index.tip2') }}</span>
              </div>
            </div>

            <div class="right">
              <div class="renzheng">
                <div>{{ $t('index.icon1') }}</div>
                <div>{{ $t('index.icon2') }}</div>
              </div>
              <div class="biaoqian">
                <div v-for="(biaoqian, index) in item.type.slice(0, 3)" :key="index">{{ biaoqian.name }}</div>
              </div>
              <div class="xingji">
                <div>
                  <span>{{ $t('index.label1') }}：</span>
                  <van-icon name="star" color="#f4b757" size="1rem" />
                  <van-icon name="star" color="#f4b757" size="1rem" />
                  <van-icon name="star" color="#f4b757" size="1rem" />
                  <van-icon name="star" color="#f4b757" size="1rem" />
                  <van-icon name="star" color="#f4b757" size="1rem" />
                </div>
              </div>
              <div class="info">
                <div>{{ item.info }}</div>
              </div>
              <div class="button2">{{ $t('index.button') }}</div>
            </div>
          </div>
        </div>
        <div class="plCard">
          <div class="title">실시간 현황</div>
          <swiper class="pl_swiper"  :options="plOption">
            <swiper-slide style="text-align: center; color: #fff;height: 1.5rem;line-height: 1.5rem;" v-for="(v, key) in plList" :key="key">{{ v }}</swiper-slide>
          </swiper>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      banners: [],
      tuis: [
        {
          id: 1,
          num: '001',
          name: '',
          pic: require("@/assets/imgs/index_pic/1.jpg"),
          avatar: require("@/assets/imgs/index_avatar/11.jpg"),
          sui: '22세/165cm/46kg'
        },
        {
          id: 2,
          num: '002',
          name: '',
          pic: require("@/assets/imgs/index_pic/2.jpg"),
          avatar: require("@/assets/imgs/index_avatar/12.jpg"),
          sui: '26세/170cm/53kg'
        },
        {
          id: 3,
          num: '003',
          name: '',
          pic: require("@/assets/imgs/index_pic/3.jpg"),
          avatar: require("@/assets/imgs/index_avatar/13.jpg"),
          sui: '21세/169cm/51kg'
        },
        {
          id: 4,
          num: '004',
          name: '',
          pic: require("@/assets/imgs/index_pic/4.jpg"),
          avatar: require("@/assets/imgs/index_avatar/14.jpg"),
          sui: '23세/165cm/49kg'
        },
        {
          id: 5,
          num: '005',
          name: '',
          pic: require("@/assets/imgs/index_pic/5.jpg"),
          avatar: require("@/assets/imgs/index_avatar/15.jpg"),
          sui: '25세/167cm/50kg'
        },
        {
          id: 6,
          num: '006',
          name: '',
          pic: require("@/assets/imgs/index_pic/6.jpg"),
          avatar: require("@/assets/imgs/index_avatar/16.jpg"),
          sui: '22세/162cm/47kg'
        },
        {
          id: 7,
          num: '007',
          name: '',
          pic: require("@/assets/imgs/index_pic/7.jpg"),
          avatar: require("@/assets/imgs/index_avatar/17.jpg"),
          sui: '34세/170cm/54kg'
        },
        {
          id: 8,
          num: '008',
          name: '',
          pic: require("@/assets/imgs/index_pic/8.jpg"),
          avatar: require("@/assets/imgs/index_avatar/18.jpg"),
          sui: '30세/166cm/49kg'
        },
        {
          id: 9,
          num: '009',
          name: '',
          pic: require("@/assets/imgs/index_pic/9.jpg"),
          avatar: require("@/assets/imgs/index_avatar/19.jpg"),
          sui: '24세/172cm/52kg'
        },
        {
          id: 10,
          num: '010',
          name: '',
          pic: require("@/assets/imgs/index_pic/10.jpg"),
          avatar: require("@/assets/imgs/index_avatar/20.jpg"),
          sui: '22세/168cm/49kg'
        }
      ],
      bannerSwiperOption: {
        autoplay: true,
      },
      xuanfeiList: [],
      gameList: [],
      plList: [
        'jx***ib 님이 회원 가입하였습니다',
        '95***8f 님이 회원 가입하였습니다',
        '71***8c 님 매칭이 완료되었습니다',
        'xf***45 님 매칭이 완료되었습니다',
        'rw***32 님 매칭이 완료되었습니다',
        '5n***8m 님 매칭이 완료되었습니다',
        'bv***f3 님이 회원 가입하였습니다',
        '44***uc 님이 회원 가입하였습니다',
        'xo***n0 님 매칭이 완료되었습니다',
        'xm***19 님이 회원 가입하였습니다',
        'ha***2g 님 매칭이 완료되었습니다',
        'or***ze 님 매칭이 완료되었습니다',
        'df***ux 님 매칭이 완료되었습니다',
        'zb***is 님 매칭이 완료되었습니다',
        'ud***c3 님이 회원 가입하였습니다',
        'n8***42 님이 회원 가입하였습니다',
        'm3***r8 님이 회원 가입하였습니다',
        'p1***5a 님이 회원 가입하였습니다',
        'ab***9e 님이 회원 가입하였습니다',
        '0v***dz 님이 회원 가입하였습니다',
        'jx***ib 님이 회원 가입하였습니다',
        '95***8f 님이 회원 가입하였습니다',
        '71***8c 님 매칭이 완료되었습니다',
        'xf***45 님 매칭이 완료되었습니다',
        'rw***32 님 매칭이 완료되었습니다',
        '5n***8m 님 매칭이 완료되었습니다',
        'bv***f3 님이 회원 가입하였습니다',
        '44***uc 님이 회원 가입하였습니다',
        'xo***n0 님 매칭이 완료되었습니다',
        'xm***19 님이 회원 가입하였습니다'
      ],
      plOption: {
        autoplay: true,
        direction: 'vertical',
        slidesPerView: 5,
        speed: 800,
      }
    }
  },
  computed: {
    swipeWidth() {
      return window.innerWidth * 0.45
    }
  },
  methods: {
    getBasicConfig() {
      this.$http({
        method: 'get',
        url: 'sys_config'
      }).then(res => {
        this.banners = res.data?.banners || [];
      })

    },

    getXuanfeiList() {
      this.$http({
        method: 'get',
        url: 'xuanfei_list'
      }).then(res => {
        if (res.code === 200) {
          this.xuanfeiList = res.data.data.map(item => {
            item.info = `신장：${item.height}cm 사이즈：${item.bust} 서비스가능지역：${item.diqu}`
            return item
          })
        } else if (res.code === 401) {
          this.$toast(res.msg);
        }
      })
    },

    goDetail(e) {
      localStorage.setItem('xuanfeiDetail', JSON.stringify(e))
      this.$router.push(`XuanfeiDetail`);
    },

    goGame(e) {
      if(!localStorage.getItem('token')){
        this.$router.push({path:'/Login'})
      }else {
        this.$router.push({path:'/WindowLottery?key='+e.key+"&id="+e.id})
      }
    },

    
    getGameItem(){
      this.$http({
        method: 'get',
        url: 'lottery_list'
      }).then(res=>{
        this.gameList = res.data;
      })
    },
  },
  created() {
    this.getBasicConfig();
    this.getGameItem()
    this.getXuanfeiList()
  }
}
</script>


<style lang="less" scoped>

#content{
  padding-bottom: 3.2rem;
}


.banner_swiper {
  // padding-top: 5%;
  height: 100%;
  width: 100%;

  .swiper-slide {
    // border-radius: 1.25rem;
    display: flex;
    justify-content: center;
    align-items: center;
    // width: 19.375rem;
    width: 100%;
    height: 10.375rem;
    text-align: center;
    font-weight: bold;
    font-size: 0.625rem;
    background-color: #ffffff;
    background-position: center;
    background-size: cover;
    color: #ffffff;
  }

  ::v-deep .swiper-container-3d .swiper-slide-shadow-left {
    background-image: linear-gradient(to left, rgba(0, 0, 0, 0), rgba(0, 0, 0, 0));
  }

  ::v-deep .swiper-container-3d .swiper-slide-shadow-right {
    background-image: linear-gradient(to right, rgba(0, 0, 0, 0), rgba(0, 0, 0, 0));
  }

  .banner_img {
    border-radius: 0;
    width: 100%;
    height: 100%;
  }
}

.content {
  width: 90%;
  margin: 0 auto;

  .txt {
    font-size: .9375rem;
    color: #000;
    margin-top: 5%;
    margin-bottom: 5%;
    .icon{
      display: inline-block;
      margin-left: .46667rem;
      border-radius: .46667rem 0 .46667rem 0;
      background: #3f3a5b;
      color: #ebcaaf;
      padding: .04rem .6rem;
      font-size: 0.88rem;
    }
  }


  .swiper_wrap {
    overflow-x: auto;

    .swiper {
      display: flex;

      .swipe {
        width: 8.67rem;
        height: 8.67rem;
        overflow: hidden;
        margin-right: .67rem;
        display: flex;
        justify-content: center;
        align-items: center;
        position: relative;
        border-radius: .33333rem;

        .tip {

          position: absolute;
          z-index: 9;
          background-color: #ebcaaf;
          color: #8d684b;
          width: 6.8rem;
          height: 6.8rem;
          transform: rotate(45deg);
          left: 5.66667rem;
          top: -4rem;

          span {

            position: absolute;
            bottom: 0;
            left: 1.6rem;
            font-size: .76667rem;
            font-weight: 600;
          }
        }
      }
    }
  }

  .xuanfeiList {
    .row {
      display: flex;
      width: 100%;
      border-bottom: 0.06rem solid #eee;
      padding-bottom: .2rem;
      margin-bottom: 0.67rem;
      align-items: center;

      .left {
        width: 7rem;
        text-align: center;
        color: #000;
        position: relative;
        overflow: hidden;
        border-radius: .4rem;

        ::v-deep .van-image__img {
          border-radius: .4rem;
        }

        .tip {
          position: absolute;
          transform: rotate(45deg);
          right: -5.66667rem;
          width: 6.66667rem;
          height: 6.66667rem;
          top: -3.33333rem;
          background: #ebcaaf;
          color: #8d684b;
          opacity: .9;

          span {
            position: absolute;
            font-weight: 600;
            left: .8rem;
            bottom: 0;
            font-size: .8rem;
          }
        }
      }

      .right {
        flex: 1;
        padding-left: 0.67rem;
        max-width: calc(100% - 7.67rem);

        .renzheng {
          display: flex;

          >div {
            background: linear-gradient(180deg, #e7caaf, #fff7ed);
            color: #a4826b;
            font-weight: 600;
            margin-left: .2rem;
            padding: .16rem .4rem;
            font-size: .73333rem;
            border-radius: .46667rem 0 .46667rem 0;
            white-space: nowrap;
            display: flex;
            align-items: center;

            &::before {
              content: '';
              width: .8125rem;
              height: .8125rem;
              background: url('/public/img/home/<USER>');
              background-size: 100% 100%;
            }
          }
        }

        .biaoqian {
          display: flex;
          flex-wrap: wrap;
          padding: 0.3rem 0;

          div {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: .2rem .4rem;
            margin: .16667rem .16667rem;
            color: #fff;
            border-radius: .33333rem;
            font-size: .76667rem;

            &:nth-child(1n) {
              background-color: #fe257f;
            }

            &:nth-child(2n) {
              background-color: #ff9702;
            }

            &:nth-child(3n) {
              background-color: #0fa7fe;
            }
          }
        }

        .xingji {
          color: #f4b757;
          font-size: .9rem;
          padding-bottom: .3rem;
        }

        .info {
          overflow: hidden;

          >div {

            font-size: .9rem;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            color: #ccc;
          }
        }

        .button2 {
          text-align: center;
          background: linear-gradient(90deg, #df35ad, #4f1db5);
          color: #fff;
          border-radius: .26667rem;
          padding: .33333rem 0;
          width: 90%;
          margin: .33333rem 0 .66667rem;
          max-width: 20rem;
        }
      }
    }
  }

  .ganeGroup{
    display: flex;
    .gameItem{
      flex: 1;
      text-align: center;
    }
    .image{
      width: 70%;
      height: 6rem;
      margin: 0 auto;
    }
    .label{
      font-size: 1rem;
      padding-top: .7rem;
      color: #34196a;
    }
    .van-image{
      width: 100%;
      height: 100%;
    }
  }

  .plCard{
    width: 96%;
    margin: 0 auto 1.25rem;
    height: 11.25rem;
    padding: 0.63rem 1.25rem;
    background: linear-gradient(180deg,#fe5986,#ff848b);
    border-radius: 0.94rem;
    .title{
      color: #ffe869;
      font-size: 1.19rem;
      text-align: center;
      margin-bottom: 0.63rem;
    }
    .pl_swiper{
      height: 7.5rem;
    }
  }
}
</style>