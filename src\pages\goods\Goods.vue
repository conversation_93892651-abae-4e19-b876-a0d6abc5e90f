<template>
  <div class="main">
    <van-nav-bar>
      <template #title>
        <span style="font-size: 1rem; color: #fff;">{{ $t('goods.title') }}</span>
      </template>
    </van-nav-bar>
    <div id="content">
      <div class="gif">
        <img src="/img/goods/top.gif" />
      </div>

      <div class="select_wrap">
        <div :class="{active: addressActive === 0}" @click="addressActive = 0">{{ $t('goods.every') }}</div>
        <div :class="{active: addressActive === index + 1}" v-for="(item, index) in addressList" :key="index" @click="addressActive = index + 1">{{ item.name }}</div>
      </div>

      <div class="view">
        <div class="left">
          <div class="card" v-for="item in xuanfeiLeftList" :key="item.id" @click="goDetail(item)">
            <div class="img_wrap">
              <img :src="item.img_url[0]">
              <div class="tip">
                <span>{{ $t('index.tip2') }}</span>
              </div>
              <div class="shadow">
                <div>
                  <van-icon name="location" color="#fff" size="13"></van-icon>
                  <span>{{ item.diqu }}</span>
                </div>
                <div class="btn">{{ $t('goods.subscribe') }}</div>
              </div>
            </div>
            <div class="row">{{ item.type[0].name }}</div>
            <div class="row">
              <div><i class="circle"></i>{{ item.xuanfei_name }}</div>
              <div>
                <van-icon name="like" color="#DC63A1"/>
                <span>{{ item.zan }}</span>
              </div>
            </div>
          </div>
        </div>
        <div class="right">
          <div class="card" v-for="item in xuanfeiRightList" :key="item.id" @click="goDetail(item)">
            <div class="img_wrap">
              <img :src="item.img_url[0]">
              <div class="tip">
                <span>{{ $t('index.tip2') }}</span>
              </div>
              <div class="shadow">
                <div>
                  <van-icon name="location" color="#fff" size="13"></van-icon>
                  <span>{{ item.diqu }}</span>
                </div>
                <div class="btn">{{ $t('goods.subscribe') }}</div>
              </div>
            </div>
            <div class="row">{{ item.type[0].name }}</div>
            <div class="row">
              <div><i class="circle"></i>{{ item.xuanfei_name }}</div>
              <div>
                <van-icon name="like" color="#DC63A1"/>
                <span>{{ item.zan }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      addressList: [],
      addressActive: 0,
      age: null,
      search: {
        class_id: 0,
        page: 1,
        limit: 999,
        name: '',
        type: 1
      },
      allXuanfeiList: [],
      ageList: []
    };
  },
  watch: {
    addressActive(newVal) {
      if(newVal === 0) {
        this.search.class_id = 0
      }else {
        this.search.class_id = this.addressList[newVal - 1].id
      }
      console.log(this.search.class_id)
      this.getXuanfeiList()
    }
  },
  created() {
    this.getAddress();
    this.getXuanfeiList();
  },
  computed: {
    xuanfeiList() {
      return this.allXuanfeiList.filter(item => {
        if (this.age) {
          return item.age === this.age
        } else {
          return true
        }
      })
    },
    xuanfeiLeftList() {
      return this.xuanfeiList.filter((item, index) => {
        return index % 2 == 0
      })
    },
    xuanfeiRightList() {
      return this.xuanfeiList.filter((item, index) => {
        return index % 2 !== 0
      })
    }
  },
  methods: {
    showTag() {
      this.$toast(this.$t('goods.tag'));
    },
    getAddress() {
      this.$http({
        method: 'post',
        url: 'xuanfei_address'
      }).then(res => {
        if (res.code === 200) {
          this.addressList = res.data
        } else if (res.code === 401) {
          this.$toast(res.msg);
        }
      })
    },
    getXuanfeiList() {
      this.$http({
        method: 'get',
        url: 'xuanfei_list',
        data: this.search
      }).then(res => {
        if (res.code === 200) {
          console.log(res)
          this.allXuanfeiList = res.data.data.map(item => {
            item.zan = parseInt(Math.random() * 30000 + 20000)
            return item
          })
          this.ageList = Array.from(new Set([...res.data.data.map(item => item.age)])).sort((a, b) => a - b)
          this.age = null
        } else if (res.code === 401) {
          this.$toast(res.msg);
        }
      })
    },

    goDetail(e) {
      localStorage.setItem('xuanfeiDetail', JSON.stringify(e))
      this.$router.push(`XuanfeiDetail`);
    }
  }
};
</script>

<style lang="less" scoped>
.main {
  padding: 0;
  background: none;
  .gif {
    width: 96%;
    margin: 0.38rem auto;
    border-radius: .8rem;
    overflow: hidden;
    img{
      width: 100%;
      height: 6rem;
      display: block;
    }
  }

  .select_wrap{
    display: flex;
    flex-wrap: wrap;
    div{
      font-size: .76667rem;
      padding: .2rem 0;
      text-align: center;
      width: calc(100% / 5);
      color: #646566;
      transition: all .2s;
      cursor: pointer;
      margin-bottom: .33333rem;
    }
    .active{
      color: #333;
      background: #e2e2e2;
      transform: scale(1.1);
      border-bottom: .06667rem solid #b979b1;
    }
  }
  
  #content{
    padding-bottom: 3.2rem;
  }


  .view {
    width: 96%;
    margin: 0 auto;
    display: flex;
    flex-wrap: wrap;
    margin-top: 4%;

    >div {
      padding-top: 3%;
      width: 48%;
      margin: 0 auto;
      border-top: 0.06rem solid #eee;
    }

    .card {
      border-radius: 0.75rem;
      position: relative;
      margin-bottom: 8%;
      .img_wrap{
        border-radius: .625rem;
        overflow: hidden;
        position: relative;
        img {
          width: 100%;
          height: 100%;
          display: block;
          border-radius: 0.625rem;
        }
        .tip {
          position: absolute;
          z-index: 1;
          top: -5.13333rem;
          right: -8.56667rem;
          width: 10rem;
          height: 10rem;
          background: #ebcaaf;
          transform: rotate(45deg);
          opacity: .8;

          span {

            position: absolute;
            bottom: 0;
            left: 1.6rem;
            font-size: 0.94rem;
            font-weight: 600;
            color: #8d684b;
          }
        }
        .shadow{
          height: 2.00rem;
          width: 100%;
          position: absolute;
          bottom: 0;
          background: rgba(0, 0, 0, .4);
          display: flex;
          justify-content: space-between;
          padding: 0.38rem 0.63rem;
          font-size: 0.85rem;
          color: #fff;
          .btn{
            border: 0.06rem solid #fff;
            padding: 0.08rem 0.38rem;
            border-radius: .13333rem;
            opacity: .9;
            font-size: 0.75rem;
          }
        }
      }
      .row{
        font-size: 0.88rem;
        color: #666;
        padding-top: 0.13rem;
        display: flex;
        justify-content: space-between;
        .circle{
          display: inline-block;
          margin-right: .2rem;
          width: .8rem;
          height: .8rem;
          border-radius: 50%;
          background: #efba8f;
        }
      }

      .tag {
        background-color: #ff2a77;
        width: 3rem !important;
        height: 2rem !important;
        border-top-left-radius: 0.56rem !important;
        font-size: 1.09375rem !important;
        color: #fff;
        position: absolute;
        display: flex;
        align-items: center;
        justify-content: center;
        border-top-left-radius: 0.9375rem;
        border-bottom-right-radius: 0.9375rem;
        top: 0;
      }

      .info {
        position: absolute;
        bottom: 5%;
        width: 100%;

        .text {
          font-size: 1.3rem;
          text-align: center;
          color: #fff;
          font-weight: 600;
        }

        .bottom {
          color: #fff;
          background-color: #ff1a6d;
          width: 60%;
          font-size: .9rem;
          margin: 0 auto;
          margin-top: 5%;
          display: flex;
          justify-content: center;
          align-items: center;
          padding-top: 3%;
          padding-bottom: 3%;
          border-radius: 0.9375rem;
        }
      }
    }
  }
}


</style>
